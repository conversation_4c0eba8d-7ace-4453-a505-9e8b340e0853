import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { DevelopersService, Developer, DevelopersResponse } from '../services/developers.service';
import Swal from 'sweetalert2';
import { BaseGridComponent } from '../../shared/base-grid/base-grid.component';

@Component({
  selector: 'app-all-developers',
  templateUrl: './all-developers.component.html',
  styleUrls: ['./all-developers.component.scss']
})
export class AllDevelopersComponent implements OnInit {

  developers: any;
  loading = false;
  searchText = '';
  currentPage = 0;
  pageSize = 10;
  totalElements = 0;
  totalPages = 0;

  // Filter options
  selectedStatus = '';
  selectedSpecialization = '';
  sortBy = 'id';
  sortDir = 'desc';

  constructor(protected cd: ChangeDetectorRef, private developersService: DevelopersService)
  { }

  ngOnInit(): void {
    this.loadDevelopers();
  }

  loadDevelopers(): void {
    this.loading = true;

    const params = {
      page: this.currentPage,
      size: this.pageSize,
      search: this.searchText || undefined,
      role: 'developer',
      sortBy: this.sortBy,
      sortDir: this.sortDir
    };

    this.developersService.getAllDevelopers(params).subscribe({
      next: (response) => {
        console.log(response);
        this.developers = response.data;
        this.totalElements = response.count;
        this.totalPages = response.count/10;
        this.loading = false;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading developers:', error);
        this.loading = false;
        Swal.fire('Error', 'Failed to load developers. Please try again.', 'error');
      }
    });
  }

  onSearchChange(searchText: string): void {
    this.searchText = searchText;
    this.currentPage = 0;
    this.loadDevelopers();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadDevelopers();
  }

  getStatusClass(status: boolean): string {
    switch (status) {
      case true:
        return 'badge-light-success';
      // case 'Suspended':
      //   return 'badge-light-warning';
      case false:
        return 'badge-light-danger';
      default:
        return 'badge-light-secondary';
    }
  }

  viewDeveloper(developer: Developer): void {
    console.log('View developer:', developer);
  }

}
