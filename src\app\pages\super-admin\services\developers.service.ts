import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface Developer {
  id: number;
  name: string;
  email: string;
  phone: string;
  specialization: string;
  experience: string;
  status: string;
  joinDate: string;
  projects: number;
  rating: number;
  avatar?: string;
  verified?: boolean;
  lastLogin?: string;
}

export interface DevelopersResponse {
  data: Developer[];
  count: number;
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

@Injectable({
  providedIn: 'root'
})
export class DevelopersService {
  private apiUrl = `${environment.apiUrl}/users`;

  constructor(private http: HttpClient) { }

  getAllDevelopers(params?: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    role?: string;
    sortBy?: string;
    sortDir?: string;
  }): Observable<DevelopersResponse> {
    let httpParams = new HttpParams();

    if (params) {
      if (params.page !== undefined) httpParams = httpParams.set('offset', params.page*10);
      if (params.size !== undefined) httpParams = httpParams.set('limit', params.size.toString());
      if (params.search) httpParams = httpParams.set('name', params.search);
      if (params.status) httpParams = httpParams.set('status', params.status);
      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);
      if (params.sortDir) httpParams = httpParams.set('sort', params.sortDir);
      if (params.role) httpParams = httpParams.set('role', params.role);
    }

    return this.http.get<DevelopersResponse>(`${environment.apiUrl}/users`, { params: httpParams });
  }






}
