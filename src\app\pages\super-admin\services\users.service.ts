import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  userType: 'Client' | 'Developer' | 'Broker' | 'Admin';
  status: string;
  joinDate: string;
  lastLogin: string;
  projects: number;
  verified: boolean;
  avatar?: string;
  address?: string;
  city?: string;
  country?: string;
  totalSpent?: number;
  activeProjects?: number;
}

export interface UsersResponse {
  data: User[];
  count: number;
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

@Injectable({
  providedIn: 'root'
})
export class UsersService {
  private apiUrl = `${environment.apiUrl}/users`;

  constructor(private http: HttpClient) { }

  /**
   * Get all users with pagination and filters
   */
  getAllUsers(params?: {
    page?: number;
    size?: number;
    search?: string;
    status?: string;
    userType?: string;
    verified?: boolean;
    sortBy?: string;
    sortDir?: string;
  }): Observable<UsersResponse> {
    let httpParams = new HttpParams();

    if (params) {
      if (params.page !== undefined) httpParams = httpParams.set('offset', params.page*10);
      if (params.size !== undefined) httpParams = httpParams.set('limit', params.size.toString());
      if (params.search) httpParams = httpParams.set('name', params.search);
      if (params.status) httpParams = httpParams.set('status', params.status);
      if (params.userType) httpParams = httpParams.set('userType', params.userType);
      if (params.verified !== undefined) httpParams = httpParams.set('verified', params.verified.toString());
      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);
      if (params.sortDir) httpParams = httpParams.set('sort', params.sortDir);
    }

    return this.http.get<UsersResponse>(this.apiUrl, { params: httpParams });
  }


}
