import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface Broker {
  id: number;
  name: string;
  email: string;
  phone: string;
  company: string;
  experience: string;
  status: string;
  joinDate: string;
  deals: number;
  commission: string;
  rating: number;
  avatar?: string;
  verified?: boolean;
  lastLogin?: string;
  totalEarnings?: number;
  activeDeals?: number;
}

export interface BrokersResponse {
  data: Broker[];
  count: number;
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

@Injectable({
  providedIn: 'root'
})
export class BrokersService {
  private apiUrl = `${environment.apiUrl}/users`;

  constructor(private http: HttpClient) { }

  /**
   * Get all brokers with pagination and filters
   */
  getAllBrokers(params?: {
    page?: number;
    size?: number;
    search?: string;
    role?: string;
    sortBy?: string;
    sortDir?: string;
  }): Observable<BrokersResponse> {
    let httpParams = new HttpParams();

    if (params) {
      if (params.page !== undefined) httpParams = httpParams.set('offset', params.page*10);
      if (params.size !== undefined) httpParams = httpParams.set('limit', params.size.toString());
      if (params.search) httpParams = httpParams.set('name', params.search);
      if (params.role) httpParams = httpParams.set('role', params.role);
      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);
      if (params.sortDir) httpParams = httpParams.set('sort', params.sortDir);
    }

    return this.http.get<any>(this.apiUrl, { params: httpParams });
  }








}
